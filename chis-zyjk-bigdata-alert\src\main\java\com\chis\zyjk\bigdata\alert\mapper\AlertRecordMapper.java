package com.chis.zyjk.bigdata.alert.mapper;

import com.chis.project.frame.starter.mybatisplus.multi.datasourse.mapper.ChisBaseMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警记录表Mapper
 */
@Mapper
public interface AlertRecordMapper extends ChisBaseMapper<AlertRecordPO> {

    /**
     * 根据自定义SQL条件查询记录列表
     *
     * @param whereSql     WHERE子句SQL（不包含WHERE关键字）
     * @param deDupValues  去重值列表，用于IN条件过滤（可选）
     * @param orderByField 排序字段
     * @return 预警记录列表
     */
    List<AlertRecordPO> listByCustomSql(@Param("whereSql") String whereSql,
                                        @Param("deDupValues") List<String> deDupValues,
                                        @Param("orderByField") String orderByField);

    /**
     * 根据自定义SQL条件查询记录列表且根据分组及排序仅查询每组前n条的数据
     *
     * @param whereSql     WHERE子句SQL（不包含WHERE关键字）
     * @param deDupValues  去重值列表，用于IN条件过滤（可选）
     * @param orderByField 排序字段
     * @param groupByField 分组字段
     * @param topN         每组取前N条
     * @return 预警记录列表
     */
    List<AlertRecordPO> listTopNByCustomSql(@Param("whereSql") String whereSql,
                                            @Param("deDupValues") List<String> deDupValues,
                                            @Param("orderByField") String orderByField,
                                            @Param("groupByField") String groupByField,
                                            @Param("topN") int topN);

}
