package com.chis.zyjk.bigdata.alert.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.zyjk.bigdata.alert.enums.ProcessStrategy;
import com.chis.zyjk.bigdata.alert.mapper.AlertRecordMapper;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRecordDTO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordLogPO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 预警记录服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertRecordService {

    private final AlertRecordMapper alertRecordMapper;

    /**
     * 根据ID查询预警记录
     *
     * @param id 主键ID
     * @return 预警记录
     */
    public AlertRecordPO getById(String id) {
        return alertRecordMapper.selectById(id);
    }

    /**
     * 分页查询预警记录
     *
     * @param page       分页参数
     * @param ruleCode   规则编码（可选）
     * @param alertLevel 预警级别（可选）
     * @param status     状态（可选）
     * @return 分页结果
     */
    public Page<AlertRecordPO> page(Page<AlertRecordPO> page, String ruleCode, String alertLevel, Integer status) {
        LambdaQueryWrapper<AlertRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ruleCode != null, AlertRecordPO::getRuleCode, ruleCode)
                .eq(alertLevel != null, AlertRecordPO::getAlertLevel, alertLevel)
                .eq(status != null, AlertRecordPO::getStatus, status)
                .eq(AlertRecordPO::getDelFlag, "0")
                .orderByDesc(AlertRecordPO::getUpdateTime);
        return alertRecordMapper.selectPage(page, queryWrapper);
    }

    /**
     * 更新预警记录状态
     *
     * @param id     主键ID
     * @param status 状态
     * @return 是否成功
     */
    public boolean updateStatus(String id, Integer status) {
        LambdaUpdateWrapper<AlertRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AlertRecordPO::getStatus, status)
                .eq(AlertRecordPO::getId, id);
        return alertRecordMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量更新预警记录状态（真正的分批处理，每批最大200条）
     *
     * @param ids    主键ID列表
     * @param status 状态
     * @return 是否成功
     */
    public boolean batchUpdateStatus(List<String> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        final int BATCH_SIZE = 999;
        int totalSize = ids.size();

        // 分批处理
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<String> batch = ids.subList(i, endIndex);
            // 批量更新当前批次的状态
            LambdaUpdateWrapper<AlertRecordPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(AlertRecordPO::getStatus, status)
                    .in(AlertRecordPO::getId, batch);
            alertRecordMapper.update(null, updateWrapper);
        }

        log.info("批量更新预警记录状态完成，总数: {}", totalSize);
        return true;
    }

    /**
     * 批量更新预警记录
     *
     * @param alertRecords 预警记录列表
     * @return 是否成功
     */
    public boolean updateBatchById(List<AlertRecordPO> alertRecords) {
        if (alertRecords == null || alertRecords.isEmpty()) {
            return true;
        }

        final int BATCH_SIZE = 999;
        int totalSize = alertRecords.size();

        // 分批处理
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<AlertRecordPO> batch = alertRecords.subList(i, endIndex);
            // 批量更新当前批次
            for (AlertRecordPO alertRecord : batch) {
                alertRecordMapper.updateById(alertRecord);
            }
        }

        log.info("批量更新预警记录完成，总数: {}", totalSize);
        return true;
    }

    /**
     * 根据自定义SQL条件查询记录列表
     *
     * @param whereSql     WHERE子句SQL（不包含WHERE关键字），支持字段计算，如：rule_code = 'RULE001' AND (alert_value * 2) > 100
     * @param deDupValues  去重值列表，用于IN条件过滤（可选）
     * @param orderByField 排序字段
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listByCustomSql(String whereSql, List<String> deDupValues, String orderByField) {
        return alertRecordMapper.listByCustomSql(whereSql, deDupValues, orderByField);
    }

    /**
     * 根据自定义SQL条件查询记录列表且根据分组及排序仅查询每组前n条的数据
     *
     * @param whereSql     WHERE子句SQL（不包含WHERE关键字），支持字段计算，如：rule_code = 'RULE001' AND (alert_value * 2) > 100
     * @param deDupValues  去重值列表，用于IN条件过滤（可选）
     * @param groupByField 分组字段（rule_code, alert_level, status等）
     * @param orderByField 排序字段（create_time, update_time等）
     * @param topN         每组取前N条
     * @return 预警记录列表
     */
    public List<AlertRecordPO> listTopNByCustomSql(String whereSql, List<String> deDupValues, String groupByField,
                                                   String orderByField, int topN) {
        return alertRecordMapper.listTopNByCustomSql(whereSql, deDupValues, groupByField, orderByField, topN);
    }

    @Transactional
    public void batchSaveOrUpdateEntities(List<AlertRecordDTO> alertRecordDTOList) {
        List<AlertRecordPO> saveList = new ArrayList<>();
        List<AlertRecordPO> updateList = new ArrayList<>();
        List<String> closeIdList = new ArrayList<>();
        for (AlertRecordDTO alertRecordDTO : alertRecordDTOList) {
            if (ProcessStrategy.CREATE.getCode().equals(alertRecordDTO.getChangeType())) {
                saveList.add(buildAlertRecord(alertRecordDTO));
            } else if (ProcessStrategy.UPDATE.getCode().equals(alertRecordDTO.getChangeType())) {
                updateList.add(updateAlertRecord(alertRecordDTO));
            } else if (ProcessStrategy.CLOSE.getCode().equals(alertRecordDTO.getChangeType())) {
                closeIdList.add(alertRecordDTO.getAlertRecord().getId());
            }
        }
        alertRecordMapper.insertBatch(saveList);
        boolean ignoreUpdate = updateBatchById(updateList);
        boolean ignoreClose = batchUpdateStatus(closeIdList, 1);
        System.out.println();
    }

    /**
     * 构建预警记录
     *
     * @param alertRecordDTO 预处理预警数据
     * @return 预警记录
     */
    private AlertRecordPO buildAlertRecord(AlertRecordDTO alertRecordDTO) {
        AlertRecordPO alertRecord = new AlertRecordPO();
        alertRecordDTO.setAlertRecord(alertRecord);
        updateAlertRecord(alertRecordDTO);
        alertRecord.setRuleCode(alertRecordDTO.getRuleCode());
        alertRecord.setDeDupValue(alertRecordDTO.getDeDupValue());
        alertRecord.setChangeType(ProcessStrategy.CREATE.getCode());
        alertRecord.setStatus(0);
        return alertRecord;
    }

    /**
     * 更新预警记录
     *
     * @param alertRecordDTO 预处理预警数据
     */
    private AlertRecordPO updateAlertRecord(AlertRecordDTO alertRecordDTO) {
        AlertRecordPO alertRecord = alertRecordDTO.getAlertRecord();
        alertRecord.setChangeType(ProcessStrategy.UPDATE.getCode());
        alertRecord.setAlertLevel(alertRecordDTO.getAlertLevel());
        alertRecord.setAlertValue(alertRecordDTO.getAlertValue());
        alertRecord.setStatus(0);

        // 使用DTO中已填充的预警内容，如果为空则使用空字符串
        String alertContent = StrUtil.isNotBlank(alertRecordDTO.getAlertContent()) ?
                alertRecordDTO.getAlertContent() : "";
        alertRecord.setAlertContent(alertContent);

        alertRecord.setAlertJson(Convert.toStr(alertRecordDTO.getAlertJson(), ""));
        alertRecord.setSourceData(Convert.toStr(alertRecordDTO.getSourceData(), ""));
        return alertRecord;
    }

    /**
     * 构建预警日志记录
     *
     * @param alertRecordDTO 预处理预警数据
     * @param changeType     变更类型
     * @return 预警日志记录
     */
    private AlertRecordLogPO buildAlertLog(AlertRecordDTO alertRecordDTO, String changeType) {
        AlertRecordPO alertRecord = alertRecordDTO.getAlertRecord();
        AlertRecordLogPO logRecord = new AlertRecordLogPO();
        logRecord.setRuleCode(alertRecord.getRuleCode());
        logRecord.setDeDupValue(alertRecord.getDeDupValue());
        logRecord.setChangeType(changeType);
        logRecord.setAlertLevel(alertRecord.getAlertLevel());
        logRecord.setAlertValue(alertRecord.getAlertValue());
        logRecord.setStatus(alertRecord.getStatus());
        logRecord.setAlertContent(alertRecord.getAlertContent());
        logRecord.setAlertJson(alertRecord.getAlertJson());
        logRecord.setSourceData(alertRecord.getSourceData());
        return logRecord;
    }
}
